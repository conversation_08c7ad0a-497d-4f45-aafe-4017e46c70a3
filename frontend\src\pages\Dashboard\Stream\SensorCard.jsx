import { useEffect, useState } from "react";
import { Typo<PERSON>, Card, CardContent, useTheme, Grid } from "@mui/material";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import s3Controller from "../../../controllers/S3.controller";
import { useApp } from "../../../hooks/AppHook";

const SensorCard = ({ stream, view, selectedStream, onSelect, artifactIndicator = {}, viewedArtifacts = [] }) => {
    const { devMode, showIDs } = useApp();
    const theme = useTheme();
    const [thumbnail, setThumbnail] = useState(null);

    useEffect(() => {
        const fetchThumbnail = async () => {
            if (stream.ThumbnailS3Key) {
                const url = await s3Controller.fetchCloudfrontSignedUrl(stream.ThumbnailS3Key);
                setThumbnail(url);
            }
        };
        fetchThumbnail();
    }, [stream.ThumbnailS3Key]);

    const artifactData = (artifactIndicator[stream.StreamName] || []).filter((a) => !viewedArtifacts.includes(a._id));
    const artifactLength = artifactData.length;

    return (
        <Grid size="auto">
            <Card
                elevation={0}
                sx={{
                    borderRadius: 0,
                    color: theme.palette.background.default,
                    cursor: selectedStream.StreamName === stream.StreamName || view === "Mosaic" ? "not-allowed" : "pointer",
                    display: "flex",
                    backgroundColor: selectedStream.StreamName === stream.StreamName || view === "Mosaic" ? "primary.main" : "primary.light",
                    transition: "0.2s",
                    ":hover": {
                        backgroundColor: selectedStream.StreamName === stream.StreamName || view === "Mosaic" ? "primary.main" : "#464F59",
                        transition: "0.2s",
                    },
                    height: { xs: "100%", lg: "auto" },
                }}
                onClick={() => {
                    if (selectedStream.StreamName === stream.StreamName || view === "Mosaic") return;
                    onSelect(stream);
                }}
            >
                <CardContent
                    sx={{
                        width: "100%",
                        padding: 0,
                        "&:last-child": {
                            paddingBottom: 0,
                        },
                    }}
                >
                    <Grid
                        container
                        alignItems={{ xs: "unset", lg: "center" }}
                        flexDirection={{ xs: "column", lg: "row" }}
                        maxWidth={{ xs: 200, lg: "100%" }}
                        gap={1.8}
                        padding={{ xs: 1, lg: 1 }}
                    >
                        <Grid
                            size={{
                                xs: "auto",
                                lg: "grow",
                            }}
                        >
                            <img
                                width={165}
                                height={95}
                                style={{ objectFit: "cover", borderRadius: "10px" }}
                                src={thumbnail || "/ship.png"}
                                alt={stream.VesselName || "Vessel"}
                            />
                        </Grid>
                        <Grid
                            container
                            flexDirection={"column"}
                            gap={1}
                            size={{
                                xs: "grow",
                                lg: "grow",
                            }}
                        >
                            <Grid>
                                <Typography fontSize={15} lineHeight={"25px"} fontWeight={600}>
                                    {`${stream.VesselName || "Unregistered"}${devMode || showIDs ? ` (${stream.StreamName})` : ""}`}
                                </Typography>
                            </Grid>
                            <Grid
                                container
                                display={"flex"}
                                justifyContent={"space-between"}
                                flexDirection={"row"}
                                alignItems={"center"}
                                gap={1}
                                size="auto"
                            >
                                <Grid display={"flex"} alignItems={"center"} size="grow">
                                    <Grid alignItems={"center"} gap={1} size="auto">
                                        <FiberManualRecordIcon
                                            sx={{
                                                display: stream.IsLive ? "block" : "none",
                                                color: theme.palette.custom.live,
                                                fontSize: "20px",
                                                lineHeight: "20px",
                                            }}
                                        />
                                    </Grid>
                                    <Grid alignItems={"center"} gap={1} size="auto">
                                        <Typography
                                            sx={{
                                                color: stream.IsLive ? theme.palette.custom.live : theme.palette.custom.offline,
                                            }}
                                            fontSize={"16px"}
                                            fontWeight={400}
                                        >
                                            {stream.IsLive ? "LIVE" : "OFFLINE"}
                                        </Typography>
                                    </Grid>
                                </Grid>
                                {artifactLength > 0 && (
                                    <Grid display={"flex"} alignItems={"center"} size="grow">
                                        <Grid alignItems={"center"} gap={1} size="auto">
                                            <Typography sx={{ color: "red" }} fontSize={"16px"} fontWeight={400}>
                                                {artifactLength}
                                            </Typography>
                                        </Grid>
                                        <Grid alignItems={"center"} gap={1} size="auto">
                                            <Grid sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                                                <img
                                                    src={"/icons/sensor_alert_icon.svg"}
                                                    alt={"Artifact Indicator"}
                                                    width={20}
                                                    height={20}
                                                    style={{ color: "red", marginLeft: "2px" }}
                                                />
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                )}
                            </Grid>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>
        </Grid>
    );
};

export default SensorCard;
