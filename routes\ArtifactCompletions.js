const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const isAuthenticated = require("../middlewares/auth");
const { validateData } = require("../middlewares/validator");
const { body } = require("express-validator");
const db = require("../modules/db");
const UserCompletionLogs = require("../models/UserCompletionLogs");
const { validateError } = require("../utils/functions");
const openai = require("openai");
const { default: rateLimit } = require("express-rate-limit");
const router = require("./S3");
const Vessel = require("../models/Vessel");
const ArtifactSuggestion = require("../models/ArtifactSuggestion");
const ArtifactSynonym = require("../models/ArtifactSynonym");
const Typo = require("typo-js");
const dictionary = new Typo("en_US");
const { cleanSuggestion } = require("../utils/functions");

const lastCheckedAt = {
    superCategories: null,
    categories: null,
    sizes: null,
    vessels: null,
    weapons: null,
    synonyms: null,
    prompt: null
};
const checkPeriodMs = 3600000; // 1 hour in ms
const dataCache = {};



// Generate a signature for data to detect changes
const generateDataSignature = (superCategories, categories, sizes, vessels, weapons, synonyms) => {
    const data = {
        superCategories: superCategories.sort(),
        categories: categories.sort(),
        sizes: sizes.sort(),
        vessels: vessels.sort(),
        weapons: weapons.sort(),
        synonyms: synonyms.map(s => `${s.type}:${s.word}:${s.synonyms.join(',')}`).sort()
    };
    return JSON.stringify(data);
};

// Helper functions for response caching
const getCachedResponse = (text, promptSignature) => {
    const cacheKey = `${text.toLowerCase().trim()}:${promptSignature}`;
    const expiry = responseCacheExpiry.get(cacheKey);

    if (expiry && Date.now() < expiry) {
        return responseCache.get(cacheKey);
    }

    // Clean up expired entry
    if (expiry) {
        responseCache.delete(cacheKey);
        responseCacheExpiry.delete(cacheKey);
    }

    return null;
};

const setCachedResponse = (text, promptSignature, response) => {
    const cacheKey = `${text.toLowerCase().trim()}:${promptSignature}`;
    const expiryTime = Date.now() + checkPeriodMs; // Cache for same duration as data cache

    responseCache.set(cacheKey, response);
    responseCacheExpiry.set(cacheKey, expiryTime);

    // Clean up old entries periodically (keep only last 1000 entries)
    if (responseCache.size > 1000) {
        const oldestKeys = Array.from(responseCache.keys()).slice(0, 100);
        oldestKeys.forEach(key => {
            responseCache.delete(key);
            responseCacheExpiry.delete(key);
        });
    }
};

const objectKeys = {
    country: "country_flags",
    color: "colors",
    size: "sizes",
    category: "categories",
    subcategory: "subcategories",
};
const openaiClient = new openai.OpenAI(process.env.OPENAI_API_KEY);
const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

const parseNLPResponse = (responseContent) => {
    const parsedResponse = JSON.parse(responseContent.replace(/```json|```|\n|\/|\\/g, ""));

    for (const key in objectKeys) {
        if (parsedResponse[key]) {
            parsedResponse[objectKeys[key]] = parsedResponse[key];
            delete parsedResponse[key];
        }
    }
    return parsedResponse;
};

const getSuperCategoriesList = async () => {
    if (!lastCheckedAt.superCategories || !dataCache.superCategories || Date.now() - lastCheckedAt.superCategories.getTime() >= checkPeriodMs) {
        dataCache.superCategories = await db.qmai.collection("analysis_results").distinct("super_category", { super_category: { $ne: null } });
        lastCheckedAt.superCategories = new Date();
    }
    return dataCache.superCategories;
};

const getCategoriesList = async () => {
    if (!lastCheckedAt.categories || !dataCache.categories || Date.now() - lastCheckedAt.categories.getTime() >= checkPeriodMs) {
        dataCache.categories = await db.qmai.collection("analysis_results").distinct("category", { category: { $ne: null } });
        lastCheckedAt.categories = new Date();
    }
    return dataCache.categories;
};

const getSizesList = async () => {
    if (!lastCheckedAt.sizes || !dataCache.sizes || Date.now() - lastCheckedAt.sizes.getTime() >= checkPeriodMs) {
        dataCache.sizes = await db.qmai.collection("analysis_results").distinct("size", { size: { $ne: null } });
        lastCheckedAt.sizes = new Date();
    }
    return dataCache.sizes;
};

const getVesselList = async () => {
    if (!lastCheckedAt.vessels || !dataCache.vessels || Date.now() - lastCheckedAt.vessels.getTime() >= checkPeriodMs) {
        const vessels = await Vessel.find({}, { name: 1 });
        dataCache.vessels = vessels.map((v) => v.name);
        lastCheckedAt.vessels = new Date();
    }
    return dataCache.vessels;
};

const getWeaponsList = async () => {
    if (!lastCheckedAt.weapons || !dataCache.weapons || Date.now() - lastCheckedAt.weapons.getTime() >= checkPeriodMs) {
        const weaponsAgg = await db.qmai
            .collection("analysis_results")
            .aggregate([
                { $match: { weapons: { $ne: null } } },
                { $group: { _id: "$weapons", count: { $sum: 1 } } },
                { $match: { count: { $gt: 10 } } },
                { $sort: { count: -1 } },
                { $project: { _id: 0, name: "$_id", count: 1 } },
            ])
            .toArray();
        dataCache.weapons = weaponsAgg.map((w) => w.name);
        lastCheckedAt.weapons = new Date();
    }
    return dataCache.weapons;
};

const getSynonymsList = async () => {
    if (!lastCheckedAt.synonyms || !dataCache.synonyms || Date.now() - lastCheckedAt.synonyms.getTime() >= checkPeriodMs) {
        dataCache.synonyms = await ArtifactSynonym.find({}, { _id: 0 });
        lastCheckedAt.synonyms = new Date();
    }
    return dataCache.synonyms;
};

const artifactCompletionsPrompt = async () => {
    // Check if we have a cached prompt that's still valid
    if (lastCheckedAt.prompt && dataCache.prompt && Date.now() - lastCheckedAt.prompt.getTime() < checkPeriodMs) {
        return dataCache.prompt;
    }

    // Fetch all required data
    const [superCategories, categories, sizes, vessels, weapons, allSynonyms] = await Promise.all([
        getSuperCategoriesList(),
        getCategoriesList(),
        getSizesList(),
        getVesselList(),
        getWeaponsList(),
        getSynonymsList()
    ]);

    // Generate data signature to check if underlying data has changed
    const currentSignature = generateDataSignature(superCategories, categories, sizes, vessels, weapons, allSynonyms);

    // If we have a cached prompt and the data hasn't changed, return cached version
    if (dataCache.prompt && dataCache.promptSignature === currentSignature) {
        // Update the timestamp to extend cache validity
        lastCheckedAt.prompt = new Date();
        return dataCache.prompt;
    }

    // Build the prompt since data has changed or no cache exists
    const synonymMap = {};
    for (const syn of allSynonyms) {
        if (!synonymMap[syn.type]) synonymMap[syn.type] = [];
        synonymMap[syn.type].push({ word: syn.word, synonyms: syn.synonyms });
    }

    // Format synonyms as numbered list per type
    let synonymString = Object.entries(synonymMap)
        .map(([type, arr]) => {
            return `${type}:\n` + arr.map((obj, idx) => `  ${idx + 1}. ${obj.word}: ${obj.synonyms.join(", ")}`).join("\n");
        })
        .join("\n\n");

    // Use a fixed reference time that updates only when cache expires to ensure consistency
    const referenceTime = new Date().toISOString();

    const prompt = `
        Extract vessel attributes from input phrase to JSON.

        Synonyms for normalization (grouped by attribute, numbered, comma-separated):
        ${synonymString}

        JSON Rules:
        1.  Attributes 'country','color','category','subcategory','size','weapons':
            *   Values always arrays.
            *   Omit key if no value.
            *   Normalize: Correct misspellings, synonyms, language, partials to standard English.(e.g. "conatiner" -> "container").
        2.  'category' values: Must be from: [${superCategories.join(",")}].
        3   'subcategory' values: Must be from: [${categories.join(",")}]
        4.  'country' values: Must be ISO 3166 names.
        5.  'size' values: Must be from: [${sizes.join(",")}].
        6.  'weapons' values: Must be from: [${weapons.join(",")}].
        7.  'vessel_name' values: Must be from: [${vessels.join(",")}].
        8.  If a vessel name is found, return it as "vessel_name" (array of strings).
        9.  If a type (image/video/both) is found, return it as "type" (default is "both").
        10.  Time:
            *   Current Ref Time: "${referenceTime}" (anchor for relative).
            *   Detect specific or relative time phrases.
            *   Output (all ISO 8601 UTC, anchored to Ref Time):
                *   Specific instant (e.g., "3 PM Feb 18 2024") -> 'time'.
                *   Period/full day (e.g., "Feb 18" [default to Ref Time year], "this month", "yesterday") -> 'start_time' (start of period), 'end_time' (end of period).
                *   Examples for relative periods (assuming Ref Time is '2024-03-15T...' for first example):
                    *   "this month": 'start_time: "2024-03-01T00:00:00.000Z"', 'end_time: "2024-03-31T23:59:59.999Z"',
                    *   "last month": 'start_time' (start of prev. month), 'end_time' (end of prev. month).
                    *   "Feb 18" (no year): Use Ref Time year. 'start_time: "YYYY-02-18T00:00:00.000Z"', 'end_time: "YYYY-02-18T23:59:59.999Z"',
            *   Final time values: full ISO 8601 UTC (e.g., 'YYYY-MM-DDTHH:mm:ss.sssZ').
            *   Omit time keys if no time found.
    `;

    // Cache the prompt and its signature
    dataCache.prompt = prompt;
    dataCache.promptSignature = currentSignature;
    lastCheckedAt.prompt = new Date();

    return prompt;
}

router.use("/", apiLimiter);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_NLP_SUGGESTIONS),
    isAuthenticated,
    (req, res, next) => validateData([body("text").isString().notEmpty().withMessage("Text needs to be string and not empty")], req, res, next),
    async (req, res) => {
        const abortController = new AbortController();
        const { signal } = abortController;
        const abortHandler = () => abortController.abort();
        res.on("close", abortHandler);
        signal.addEventListener("abort", () => {
            if (!res.headersSent) throw new Error("Client Closed Request");
        });
        try {
            // Step 1: Extract user text and get cached prompt
            const { text } = req.body;
            const prompt = await artifactCompletionsPrompt();

            // Step 2: Call OpenAI for completions (always called, but with cached prompt)
            const stream = await openaiClient.chat.completions.create({
                model: "chatgpt-4o-latest",
                messages: [
                    { role: "system", content: prompt },
                    { role: "user", content: text },
                ],
                signal,
            });

            // Step 3: Parse and validate response
            let responseContent = stream.choices[0]?.message.content;
            if (!responseContent || !responseContent.length) {
                throw "Your search does not match any known artifacts.";
            }

            // Step 4: Log completion analytics
            UserCompletionLogs.create({ user_id: req.user._id || null, command: text, response: responseContent, completion_type: "events_filter" });
            let parsedResponse = parseNLPResponse(responseContent);

            // Step 5: Vessel name to ID mapping
            let vessel_ids = [];
            if (parsedResponse && parsedResponse.vessel_name && Array.isArray(parsedResponse.vessel_name)) {
                const vessels = await Vessel.find({ name: { $in: parsedResponse.vessel_name } });
                vessel_ids = vessels.map((v) => v._id);
            } else if (parsedResponse && parsedResponse.vessel_name) {
                const vessels = await Vessel.find({ name: new RegExp(`^${parsedResponse.vessel_name}$`, "i") });
                vessel_ids = vessels.map((v) => v._id);
            }
            if (vessel_ids.length > 0) {
                parsedResponse.vessel_ids = vessel_ids;
            } else {
                parsedResponse.vessel_ids = null;
            }
            delete parsedResponse.vessel_name;

            // Step 6: Ensure type and normalize arrays
            if (!parsedResponse.type || typeof parsedResponse.type !== "string") parsedResponse.type = "both";
            const arrayFields = ["country_flags", "vessel_ids", "categories", "colors", "sizes", "weapons"];
            for (const key of arrayFields) {
                if (!Array.isArray(parsedResponse[key])) {
                    parsedResponse[key] = parsedResponse[key] ? [parsedResponse[key]] : null;
                }
            }

            // Step 7: Handle time fields
            if (parsedResponse.time && !parsedResponse.start_time && !parsedResponse.end_time) {
                parsedResponse.start_time = parsedResponse.end_time = parsedResponse.time;
                delete parsedResponse.time;
            }
            if (parsedResponse.start_time && isNaN(parsedResponse.start_time)) {
                const t = Date.parse(parsedResponse.start_time);
                parsedResponse.start_time = isNaN(t) ? null : t;
            }
            if (parsedResponse.end_time && isNaN(parsedResponse.end_time)) {
                const t = Date.parse(parsedResponse.end_time);
                parsedResponse.end_time = isNaN(t) ? null : t;
            }

            // Step 8: Filter only allowed keys
            const allowedKeys = ["type", "country_flags", "vessel_ids", "categories", "start_time", "end_time", "colors", "sizes", "weapons"];
            parsedResponse = Object.fromEntries(Object.entries(parsedResponse).filter(([k]) => allowedKeys.includes(k)));

            // Step 9: Store or update suggestion click count (normalized/corrected, always lowercase)
            if (text) {
                let cleanText = cleanSuggestion(text).toLowerCase();
                if (!dictionary.check(cleanText)) {
                    const [first] = dictionary.suggest(cleanText);
                    if (first) {
                        cleanText = first.toLowerCase();
                    }
                }
                const existing = await ArtifactSuggestion.findOne({ search: cleanText });
                if (existing) {
                    existing.click = (existing.click || 0) + 1;
                    await existing.save();
                } else {
                    await ArtifactSuggestion.create({ search: cleanText, click: 1, impressions: 0 });
                }
            }

            res.json(parsedResponse);
        } catch (err) {
            if (
                err.name === "AbortError" ||
                err.name === "CanceledError" ||
                err.message === "Client Closed Request"
            ) {
                return;
            }
            validateError(err, res);
        } finally {
            res.off("close", abortHandler);
        }
    },
);

module.exports = router;
