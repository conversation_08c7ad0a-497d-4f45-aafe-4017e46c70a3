// Import required libraries
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from "chart.js";
import annotationPlugin from "chartjs-plugin-annotation";

// Register components in Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, Title, Tooltip, Legend, annotationPlugin);

export default function DoughnutChart({ data, options, fontSizes }) {
    fontSizes = fontSizes || [
        { size: "20rem", family: "Arial" },
        { size: "40rem", family: "Arial" },
    ];

    const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false, // Prevent resizing based on aspect ratio
        cutout: "70%",
        plugins: {
            annotation: {
                animations: false,
                annotations: {
                    dLabel: {
                        type: "doughnutLabel",
                        content: ({ chart }) => {
                            const dataset = chart.data.datasets[0];
                            const total = dataset.data.reduce((sum, value) => sum + value, 0);
                            return ["Total Detections", total.toString()];
                        },
                        font: fontSizes,
                        color: "#FFFFFF",
                    },
                },
            },
            legend: {
                position: "bottom",
                labels: {
                    color: "#FFFFFF",
                    usePointStyle: true,
                    pointStyle: "circle",
                },
            },
            tooltip: {
                callbacks: {
                    label: function (tooltipItem) {
                        return `${tooltipItem.label}: ${tooltipItem.raw}`;
                    },
                },
            },
        },
    };

    const mergedOptions = { ...defaultOptions, ...options };

    return <Doughnut data={data} options={mergedOptions} />;
}
