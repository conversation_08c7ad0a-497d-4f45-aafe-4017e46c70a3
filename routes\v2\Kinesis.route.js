const express = require("express");
const router = express.Router();
const assignEndpointId = require("../../middlewares/assignEndpointId");
const isAuthenticated = require("../../middlewares/auth");
const { validateData } = require("../../middlewares/validator");
const { endpointIds } = require("../../utils/endpointIds");
const { validateError, canAccessVessel, userHasPermissions } = require("../../utils/functions");
const awsKinesis = require("../../modules/awsKinesis");
const { query } = require("express-validator");
const { default: rateLimit } = require("express-rate-limit");
const vesselService = require("../../services/Vessel.service");
const streamService = require("../../services/Stream.service");
const { permissions } = require("../../utils/permissions");

const authUserApiLimiter = rateLimit({
    windowMs: 10 * 1000,
    limit: 50,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

// Middleware to apply the correct limiter
function conditionalRateLimiter(req, res, next) {
    if (req.user) {
        authUserApiLimiter(req, res, next);
    } else {
        apiLimiter(req, res, next);
    }
}

router.use("/", conditionalRateLimiter);

let tts = Date.now();
router.get(
    "/dashStreamingSessionURL",
    (req, res, next) => {
        tts = Date.now();
        next();
    },
    assignEndpointId.bind(this, endpointIds.FETCH_STREAM_URL_V2),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("streamName")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("streamMode")
                    .isString()
                    .notEmpty()
                    .toUpperCase()
                    .isIn(["LIVE", "ON_DEMAND", "LIVE_REPLAY"])
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("minutes")
                    .isNumeric()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                query("totalDuration")
                    .isNumeric()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { streamName, region, streamMode, minutes, totalDuration } = req.query;

            const stream = await streamService.fetchSingle({ unitId: streamName });
            if (!stream) return res.status(404).json({ message: "Stream does not exist" });

            if (req.api_key || (req.user && !userHasPermissions(req.user, [permissions.accessAllVessels]))) {
                const vessel = await vesselService.findByAssignedUnitId({ unitId: stream.unit_id });
                if (!vessel) return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                if (!canAccessVessel(req, vessel)) {
                    return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                }
            }

            const ts = Date.now();
            const data = await awsKinesis.getDashStreamingSessionURL_V2({ streamName, region, streamMode, minutes, totalDuration });
            console.log("[dashStreamingSessionURL] session url time taken", Date.now() - ts, "ms");

            console.log("[dashStreamingSessionURL] total time taken", Date.now() - tts, "ms");
            res.json({ data });
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
