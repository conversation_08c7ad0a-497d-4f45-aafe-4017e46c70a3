import { <PERSON><PERSON>, <PERSON>b, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge, Typo<PERSON> } from "@mui/material";
import { useEffect, useMemo, useState, memo, useRef } from "react";
import { useApp } from "../../../hooks/AppHook";
import { useUser } from "../../../hooks/UserHook";
import Events from "./Events";
import theme from "../../../theme";
import axiosInstance from "../../../axios.js";
import Favourites from "./Favourite/Favourites.jsx";
import { useLocation } from "react-router-dom";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";
import ArtifactAutoSuggest from "./ArtifactAutoSuggest";

const EventsManagement = () => {
    const { user } = useUser();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const [tab, setTab] = useState("");
    const [filters, setFilters] = useState({});
    const [hasVisitedEvents, setHasVisitedEvents] = useState(false);
    const location = useLocation();

    const [showFilterModal, setShowFilterModal] = useState(false);
    const [vessels, setVessels] = useState([]);
    const [showingFor, setShowingFor] = useState(null);
    const [originalInput, setOriginalInput] = useState("");
    const [isLoading, setIsLoading] = useState(false);

    const { isMobile, devMode } = useApp();
    const onCompletionsStart = useRef(null);
    const abortControllerRef = useRef();

    useEffect(() => {
        const isEventPath = location.pathname === "/dashboard/events" || location.pathname.startsWith("/dashboard/events");

        if (isEventPath) {
            setHasVisitedEvents(true);
        }
    }, [location]);

    const tabs = useMemo(
        () => [
            {
                value: "events",
                label: "Events",
                component: vessels.length > 0 && (
                    <Events
                        showFilterModal={showFilterModal}
                        setShowFilterModal={setShowFilterModal}
                        filters={filters}
                        setFilters={setFilters}
                        vessels={vessels}
                        tab={tab}
                        onCompletionsStart={onCompletionsStart}
                        onLoadingChange={setIsLoading}
                    />
                ),
                display: true,
            },
            {
                value: "favourites",
                label: "Favorites",
                component: vessels.length > 0 && <Favourites vessels={vessels} />,
                display: true,
            },
        ],
        [user, showFilterModal, filters, vessels, tab],
    );

    const filterVessels = (vessels) => {
        if (!vessels || !Array.isArray(vessels)) return [];

        return vessels.filter((vessel) => {
            if (vessel.is_active === false && !devMode) return false;
            return true;
        });
    };

    const fetchVessels = async () => {
        try {
            if (vesselInfo) {
                const filteredVessels = filterVessels(vesselInfo);
                setVessels(filteredVessels);
            } else {
                fetchVesselsInfo();
            }
        } catch (err) {
            console.error("An error occurred while fetching vessels on the events Page:", err);
        }
    };

    useEffect(() => {
        fetchVessels();
    }, [vesselInfo]);

    useEffect(() => {
        if (!tab) {
            setTab(tabs.find((t) => t.display)?.value || "");
        }
    }, [tabs]);

    const handleAutoSuggestSelect = async (suggestion, input) => {
        // If Enter is pressed on empty string, clear filters
        if (suggestion === "") {
            setFilters({});
            setOriginalInput("");
            setShowingFor(null);
            return;
        }
        if (input && suggestion && suggestion !== input) {
            setOriginalInput(input);
            setShowingFor(suggestion);
        } else {
            setOriginalInput("");
            setShowingFor(null);
        }
        try {
            onCompletionsStart.current?.();
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
            abortControllerRef.current = new AbortController();
            const res = await axiosInstance.post(
                "/completions",
                { text: suggestion },
                { signal: abortControllerRef.current.signal, meta: { showSnackbar: false } }
            );
            abortControllerRef.current = null;
            if (res.data && typeof res.data === "object") {
                setFilters(res.data);
            }
        } catch (err) {
            console.error("Error fetching completions for suggestion", err);
        }
    };

    const handleTabChange = (event, newValue) => {
        setTab(newValue);
    };

    if (!hasVisitedEvents) {
        return null;
    }

    const activeFilterCount = Object.entries(filters).filter(([key, v]) => {
        if (!v || (Array.isArray(v) && v.length === 0) || (!Array.isArray(v) && v === "")) return false;
        if (key === "type" && v === "both") return false;
        if (key === "end_time" && v) return false;
        return true;
    }).length;

    return (
        user &&
        tabs.some((t) => t.display) &&
        tab && (
            <Grid
                container
                color={"#FFFFFF"}
                flexDirection={"column"}
                width={"100%"}
                height={"100%"}
                overflow={"auto"}
                sx={{ backgroundColor: theme.palette.custom.darkBlue }}
            >
                <Grid
                    container
                    padding={2}
                    display={"flex"}
                    columnGap={{ xs: 2, lg: 0 }}
                    rowGap={2}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    flexWrap={"wrap"}
                >
                    <Grid
                        size={{
                            xs: "grow",
                            lg: 4.5,
                        }}
                    >
                        <Tabs
                            value={tab}
                            onChange={handleTabChange}
                            sx={{
                                width: "100%",
                                padding: "4px",
                                border: `2px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                backgroundColor: "transparent",
                                "& .MuiTabs-flexContainer": {
                                    height: "100%",
                                },
                                "& .MuiButtonBase-root": {
                                    width: "50%",
                                    borderRadius: "8px",
                                },
                                "& .MuiButtonBase-root.Mui-selected": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                        >
                            {tabs
                                .filter((t) => t.display)
                                .map((t) => (
                                    <Tab
                                        key={t.value}
                                        label={t.label}
                                        value={t.value}
                                        sx={{
                                            maxWidth: "none",
                                        }}
                                    />
                                ))}
                        </Tabs>
                    </Grid>
                    {tab === "events" && (
                        <Grid
                            container
                            columnGap={2}
                            justifyContent={"space-between"}
                            size={{
                                xs: 12,
                                lg: 7.4,
                            }}
                        >
                            <Grid
                                size={{
                                    xs: "grow",
                                    lg: 5.8,
                                }}
                            >
                                <ArtifactAutoSuggest onSelect={handleAutoSuggestSelect} isLoading={isLoading} />
                            </Grid>
                            <Grid alignItems={"center"} display={"flex"} justifyContent={"flex-end"} gap={2} size="auto">
                                <Badge
                                    badgeContent={activeFilterCount}
                                    color="primary"
                                    invisible={activeFilterCount === 0}
                                    sx={{
                                        height: isMobile ? "100%" : "auto",
                                        "& .MuiBadge-badge": {
                                            backgroundColor: theme.palette.custom.mainBlue,
                                            color: "#FFFFFF",
                                            borderRadius: "50%",
                                            height: 20,
                                            width: 20,
                                            border: "2px solid #FFFFFF",
                                            top: 3,
                                            right: 3,
                                        },
                                    }}
                                >
                                    <Button
                                        className="events-step-1"
                                        variant="outlined"
                                        startIcon={<img src={"/icons/filter_icon.svg"} width={20} height={20} alt={"Filter"} />}
                                        sx={{
                                            "&.MuiButtonBase-root": {
                                                borderColor:
                                                    activeFilterCount === 0 ? theme.palette.custom.borderColor : theme.palette.custom.mainBlue,
                                                height: { xs: "100%", lg: "auto" },
                                                color: "#FFFFFF",
                                                padding: { xs: "0", lg: "10px 20px" },
                                                fontWeight: "bold",
                                            },
                                            "& .MuiButton-icon": {
                                                marginRight: { xs: 0, lg: "10px" },
                                            },
                                        }}
                                        onClick={() => setShowFilterModal(true)}
                                        disabled={isLoading}
                                    >
                                        {!isMobile && "Filter"}
                                    </Button>
                                </Badge>
                            </Grid>
                        </Grid>
                    )}
                </Grid>
                {tab === "events" && showingFor && originalInput && (
                    <Grid item xs={12} padding={2} pt={0}>
                        <Typography sx={{ fontWeight: "bold", fontSize: 16, pl: 1 }}>
                            <span style={{ color: theme.palette.custom.mainBlue }}>Showing results for</span> &quot;{showingFor}&quot;{" "}
                            <span style={{ textDecoration: "line-through", color: "#888", marginLeft: 8 }}>{originalInput}</span>
                        </Typography>
                    </Grid>
                )}
                {tabs
                    .filter((t) => t.display)
                    .map((t) => (
                        <Grid key={t.value} display={tab !== t.value && "none"} paddingX={2} paddingBottom={2} width={"100%"} size="grow">
                            {t.component}
                        </Grid>
                    ))}
            </Grid>
        )
    );
};

export default memo(EventsManagement);
