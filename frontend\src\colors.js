const basicColors = [
    { name: "black", hex: "#000000" },
    { name: "white", hex: "#FFFFFF" },
    { name: "red", hex: "#FF0000" },
    { name: "green", hex: "#00FF00" },
    { name: "blue", hex: "#0000FF" },
    { name: "yellow", hex: "#FFFF00" },
    { name: "cyan", hex: "#00FFFF" },
    { name: "magenta", hex: "#FF00FF" },
    { name: "silver", hex: "#C0C0C0" },
    { name: "gray", hex: "#808080" },
    { name: "maroon", hex: "#800000" },
    { name: "olive", hex: "#808000" },
    { name: "purple", hex: "#800080" },
    { name: "teal", hex: "#008080" },
    { name: "navy", hex: "#000080" },
    { name: "orange", hex: "#FFA500" },
    { name: "brown", hex: "#A52A2A" },
    { name: "lime", hex: "#00FF00" },
    { name: "indigo", hex: "#4B0082" },
    { name: "violet", hex: "#EE82EE" },
    { name: "pink", hex: "#FFC0CB" },
    { name: "gold", hex: "#FFD700" },
    { name: "beige", hex: "#F5F5DC" },
    { name: "coral", hex: "#FF7F50" },
    { name: "crimson", hex: "#DC143C" },
    { name: "khaki", hex: "#F0E68C" },
    { name: "lavender", hex: "#E6E6FA" },
    { name: "salmon", hex: "#FA8072" },
    { name: "tan", hex: "#D2B48C" },
    { name: "turquoise", hex: "#40E0D0" },
    { name: "aquamarine", hex: "#7FFFD4" },
    { name: "azure", hex: "#F0FFFF" },
    { name: "chartreuse", hex: "#7FFF00" },
    { name: "chocolate", hex: "#D2691E" },
    { name: "plum", hex: "#DDA0DD" },
    { name: "orchid", hex: "#DA70D6" },
    { name: "rose", hex: "#FF007F" },
    { name: "mint", hex: "#98FF98" },
    { name: "peach", hex: "#FFDAB9" },
    { name: "apricot", hex: "#FBCEB1" },
    { name: "amber", hex: "#FFBF00" },
    { name: "mustard", hex: "#FFDB58" },
    { name: "sky", hex: "#87CEEB" },
    { name: "sea", hex: "#2E8B57" },
    { name: "grass", hex: "#7CFC00" },
    { name: "sand", hex: "#C2B280" },
    { name: "stone", hex: "#888888" },
    { name: "smoke", hex: "#737373" },
    { name: "ice", hex: "#AFEEEE" },
];

function getColorList(type = "name") {
    if (type === "hex") {
        return basicColors.map((c) => c.hex);
    }
    return basicColors.map((c) => c.name);
}

export { basicColors, getColorList };
