import { Button, Grid, InputAdornment, OutlinedInput, Tab, Tabs } from "@mui/material";
import { Add, Search } from "@mui/icons-material";
import { useEffect, useMemo, useState } from "react";
import { useUser } from "../../../hooks/UserHook";
import { isAllowedUser, permissions } from "../../../utils";
import Vessels from "./Vessels";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";

export default function VesselManagement() {
    const { user } = useUser();
    const [tab, setTab] = useState("");
    const { isMobile } = useApp();

    const [showAddVesselModal, setShowAddVesselModal] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");

    const isVesselEditDisabled = useMemo(() => {
        if (!user || !user._id) return true;
        return !isAllowedUser(user);
    }, [user]);

    const tabs = useMemo(
        () => [
            {
                value: "vessels",
                label: "Vessels",
                component: (
                    <Vessels
                        showAddVessel={showAddVesselModal}
                        setShowAddVessel={setShowAddVesselModal}
                        searchQuery={searchQuery}
                        isVesselEditDisabled={isVesselEditDisabled}
                    />
                ),
                display: user?.hasPermissions([permissions.manageVessels]),
            },
        ],
        [user, showAddVesselModal, searchQuery, isVesselEditDisabled],
    );

    const handleSearchChange = (event) => {
        setSearchQuery(event.target.value);
    };

    useEffect(() => {
        if (!tab) {
            setTab(tabs.find((t) => t.display)?.value || "");
        }
    }, [tabs]);

    return (
        user &&
        tabs.some((t) => t.display) &&
        tab && (
            <Grid
                container
                direction="column"
                sx={{
                    color: "#FFFFFF",
                    height: "100%",
                    overflow: "auto",
                    backgroundColor: theme.palette.custom.darkBlue,
                }}
            >
                <Grid container padding={2} display={"flex"} rowGap={2} justifyContent={"space-between"} alignItems={"center"} flexWrap={"wrap"}>
                    <Grid size={{ xs: 12, lg: 2.5 }}>
                        <Tabs
                            value={tab}
                            onChange={(_, v) => setTab(v)}
                            sx={{
                                width: "100%",
                                padding: "4px",
                                border: `2px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                backgroundColor: "transparent",
                                "& .MuiTabs-flexContainer": {
                                    height: "100%",
                                },
                                "& .MuiButtonBase-root": {
                                    width: "100%",
                                    borderRadius: "8px",
                                },
                                "& .MuiButtonBase-root.Mui-selected": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                        >
                            {tabs
                                .filter((t) => t.display)
                                .map((t) => (
                                    <Tab
                                        key={t.value}
                                        label={t.label}
                                        value={t.value}
                                        sx={{
                                            maxWidth: "none",
                                        }}
                                    />
                                ))}
                        </Tabs>
                    </Grid>
                    <Grid container columnGap={2} justifyContent={"space-between"} size={{ xs: 12, lg: 9.4 }}>
                        <Grid size={{ xs: "grow", lg: 5.8 }}>
                            <OutlinedInput
                                type="text"
                                value={searchQuery}
                                onChange={handleSearchChange}
                                startAdornment={
                                    <InputAdornment position="start">
                                        <Search sx={{ color: "#FFFFFF" }} />
                                    </InputAdornment>
                                }
                                placeholder="Search by vessel name or unit ID"
                                sx={{
                                    color: "#FFFFFF",
                                    width: "100%",
                                    "& .MuiOutlinedInput-notchedOutline": {
                                        border: "2px solid",
                                        borderColor: theme.palette.custom.borderColor + " !important",
                                        borderRadius: "8px",
                                    },
                                }}
                            />
                        </Grid>
                        <Grid alignItems={"center"} display={"flex"} justifyContent={"flex-end"} gap={2} size="auto">
                            <Button
                                variant="contained"
                                sx={{
                                    "&.MuiButtonBase-root": {
                                        color: "#FFFFFF",
                                        height: { xs: "100%", lg: "auto" },
                                        padding: { xs: 0, lg: "10px 20px" },
                                        backgroundColor: theme.palette.custom.mainBlue,
                                        fontWeight: "bold",
                                    },
                                    "&.MuiButtonBase-root.Mui-disabled": {
                                        backgroundColor: "#9A9CA2",
                                    },
                                    "& .MuiButton-icon": {
                                        marginRight: { xs: 0, lg: "10px" },
                                    },
                                }}
                                startIcon={<Add />}
                                onClick={() => setShowAddVesselModal(true)}
                                disabled={isVesselEditDisabled}
                            >
                                {!isMobile && "Add Vessel"}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
                {tabs
                    .filter((t) => t.display)
                    .map((t) => (
                        <Grid
                            key={t.value}
                            size="grow"
                            sx={{
                                display: tab !== t.value ? "none" : "block",
                                px: 2,
                                pb: 2,
                                width: "100%",
                            }}
                        >
                            {t.component}
                        </Grid>
                    ))}
            </Grid>
        )
    );
}
