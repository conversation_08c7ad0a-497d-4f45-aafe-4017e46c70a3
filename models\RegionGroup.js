const mongoose = require("mongoose");
const db = require("../modules/db");
const { normalizeName } = require("../utils/functions");

const regionGroupSchema = new mongoose.Schema({
    name: { type: String, required: true, unique: true },
    timezone: { type: String, required: true },
    unit_ids: { type: Array, required: true },
    vessel_ids: [{ type: mongoose.Schema.Types.ObjectId, required: true }],
    created_by: { type: mongoose.Schema.Types.ObjectId, required: true },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});
// Normalize name before saving
regionGroupSchema.pre("save", function (next) {
    if (this.name) {
        this.name = normalizeName(this.name);
    }
    next();
});
// Ensure case-insensitive uniqueness
regionGroupSchema.index({ name: 1 }, { unique: true, collation: { locale: "en", strength: 2 } });

const RegionGroup = db.qm.model("RegionGroup", regionGroupSchema, "regions_groups");

module.exports = RegionGroup;
