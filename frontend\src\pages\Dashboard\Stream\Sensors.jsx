import { useMemo } from "react";
import { Grid, Typography, Stack, Skeleton } from "@mui/material";
import { useApp } from "../../../hooks/AppHook";
import RegionGroupFilter from "../../../components/RegionGroupFilter";
import SensorCard from "./SensorCard";

const Sensors = ({
    view = "Single",
    loadingStreams,
    streams,
    selectedStream,
    setSelectedStream,
    fetchStreams,
    artifactIndicator,
    viewedArtifacts,
    selectedRegionGroup,
    setSelectedRegionGroup,
    regionGroups,
}) => {
    const { screenSize } = useApp();

    const filteredStreams = useMemo(() => {
        if (selectedRegionGroup === "all") return streams.filter((stream) => stream.Tags.Name != "Unregistered");
        if (selectedRegionGroup === "allUnregistered") return streams;
        return streams.filter((stream) => stream.RegionGroupId === selectedRegionGroup);
    }, [streams, selectedRegionGroup]);

    if (loadingStreams)
        return (
            <Stack direction={{ xs: "row", lg: "column" }} gap={"1px"} width={"100%"}>
                {Array.from({ length: screenSize.xs ? 4 : screenSize.md ? 7 : 3 }).map((_, index) => (
                    <Skeleton key={index} animation="wave" variant="rectangular" height={100} sx={{ width: { xs: 200, lg: "auto" } }} />
                ))}
            </Stack>
        );

    if (streams.length == 0)
        return (
            <Typography color={"#FFFFFF"} padding={"10px"} textAlign={"center"} sx={{ backgroundColor: "primary.light" }}>
                No sensors found
            </Typography>
        );

    return (
        <>
            {loadingStreams ? (
                <Stack direction={{ xs: "row", lg: "column" }} gap={"1px"}>
                    {Array.from({ length: screenSize.xs ? 4 : screenSize.md ? 7 : 3 }).map((_, index) => (
                        <Skeleton key={index} animation="wave" variant="rectangular" height={120} sx={{ width: { xs: 200, lg: "auto" } }} />
                    ))}
                </Stack>
            ) : streams.length == 0 ? (
                <Typography color={"#FFFFFF"} padding={"10px"} textAlign={"center"} sx={{ backgroundColor: "primary.light" }}>
                    No sensors in selected region
                </Typography>
            ) : (
                <Grid
                    container
                    className="dashboard-step-8"
                    flexDirection={{ xs: "row", lg: "column" }}
                    wrap="nowrap"
                    height={"auto"}
                    minWidth={"350px"}
                    maxHeight={400}
                    overflow={"auto"}
                >
                    <Grid
                        sx={{ position: "absolute", top: { xs: "30px", md: "30px", lg: "8px" }, right: { xs: "10px", md: "10px", lg: "45px" } }}
                        display={regionGroups.length > 1 ? "block" : "none"}
                    >
                        <RegionGroupFilter regionGroups={regionGroups} value={selectedRegionGroup} onChange={setSelectedRegionGroup} />
                    </Grid>
                    {filteredStreams.map((stream) => (
                        <SensorCard
                            key={stream.StreamName}
                            stream={stream}
                            view={view}
                            selectedStream={selectedStream}
                            onSelect={(stream) => {
                                setSelectedStream(stream);
                                fetchStreams();
                            }}
                            artifactIndicator={artifactIndicator}
                            viewedArtifacts={viewedArtifacts}
                        />
                    ))}
                </Grid>
            )}
        </>
    );
};

export default Sensors;
