const { default: mongoose } = require("mongoose");
const ArtifactFavourite = require("../models/ArtifactFavourites");
const db = require("../modules/db");
const { validateError, canAccessVessel } = require("../utils/functions");
const { permissions } = require("../utils/permissions");
const Vessel = require("../models/Vessel");

const isVesselProvisioned = async (user, vessel_id) => {
    if (!user) throw new Error("User is required");
    if (!user.permissions) throw new Error("User permissions are required");
    if (!vessel_id) throw new Error("Vessel ID is required");

    if (user.permissions.find((p) => p.permission_id === permissions.accessAllVessels)) {
        return true;
    } else {
        const vessel = await Vessel.findOne({ _id: new mongoose.Types.ObjectId(vessel_id) }, { _id: 1, is_active: 1 });
        console.log("vessel", vessel);
        if (!vessel) {
            console.warn(`[ArtifactFavourites.service.isVesselProvisioned] Vessel ${vessel_id} not found`);
            return false;
        }

        return canAccessVessel({ user }, vessel);
    }
};

async function addFavouriteArtifact(req, res) {
    try {
        const { _id: user_id } = req.user;
        const { artifact_id } = req.body;

        if (!user_id || !artifact_id) {
            return res.status(400).send({ message: "user_id and artifact_id are required" });
        }

        const artifact = await db.qmai
            .collection("analysis_results")
            .findOne({ _id: new mongoose.Types.ObjectId(artifact_id) }, { projection: { _id: 1, onboard_vessel_id: 1 } });
        if (!artifact) {
            return res.status(404).send({ message: "Artifact not found" });
        }

        if (!(await isVesselProvisioned(req.user, artifact.onboard_vessel_id))) {
            return res.status(403).send({ message: "Vessel is not assigned to you" });
        }

        const isAlreadyExists = await ArtifactFavourite.findOne({ user_id, artifact_id });
        if (isAlreadyExists) {
            return res.status(400).send({ message: "Artifact already in favourites" });
        }

        const newFavourite = new ArtifactFavourite({ user_id, artifact_id });
        await newFavourite.save();

        res.status(201).send({ message: "Artifact added to favourites" });
    } catch (error) {
        validateError(error, res);
    }
}

// there is currently no usecase for this
// async function getAllFavouriteArtifacts(req, res) {
//     try {
//         const favourites = await ArtifactFavourite.find({});
//         res.status(200).send(favourites);
//     } catch (error) {
//         validateError(error, res);
//     }
// }

async function deleteFavouriteArtifact(req, res) {
    try {
        const { _id: user_id } = req.user;
        const { artifact_id } = req.body;

        if (!user_id || !artifact_id) {
            return res.status(400).send({ message: "user_id and artifact_id are required" });
        }

        const result = await ArtifactFavourite.findOneAndDelete({ user_id, artifact_id });
        if (!result) {
            return res.status(404).send({ message: "Artifact not found in favourites" });
        }

        res.status(200).send({ message: "Artifact removed from favourites" });
    } catch (error) {
        validateError(error, res);
    }
}

async function getUserFavouriteArtifacts(req, res) {
    try {
        const { _id: user_id } = req.user;

        if (!user_id) {
            return res.status(400).send({ message: "user_id is required" });
        }

        const allFavorites = await ArtifactFavourite.find({ user_id }).select("artifact_id");
        const artifactIds = allFavorites.map((fav) => fav.artifact_id);

        const query = {
            _id: { $in: artifactIds },
        };
        if (!req.user.permissions.find((p) => p.permission_id === permissions.accessAllVessels)) {
            const activeVessels = await Vessel.find({ _id: { $in: req.user.allowed_vessels }, is_active: true }, { _id: 1 });
            query.onboard_vessel_id = { $in: activeVessels.map((v) => v._id) };
        }

        const artifacts = await db.qmai
            .collection("analysis_results")
            .find(query, {
                projection: {
                    _id: 1,
                    unit_id: 1,
                    bucket_name: 1,
                    image_path: 1,
                    video_path: 1,
                    location: 1,
                    category: 1,
                    super_category: 1,
                    size: 1,
                    color: 1,
                    weapons: 1,
                    others: 1,
                    timestamp: 1,
                    onboard_vessel_name: 1,
                    onboard_vessel_id: 1,
                    country_flag: 1,
                    aws_region: 1,
                    text_extraction: 1,
                    imo_number: 1,
                },
            })
            .toArray();

        // ensure favorite artifacts only include the ones from the database. artifacts may get deleted from DB
        const favourites = allFavorites.filter((fav) => artifacts.some((art) => art._id.toString() === fav.artifact_id.toString()));
        res.status(200).send({ favourites, artifacts });
    } catch (error) {
        validateError(error, res);
    }
}

module.exports = {
    addFavouriteArtifact,
    deleteFavouriteArtifact,
    getUserFavouriteArtifacts,
};
