import axiosInstance from "../axios";

class RegionGroupController {
    async fetchAll() {
        const response = await axiosInstance.get("/regionGroups");
        return response.data;
    }

    async create({ name, timezone, vessel_ids = [] }) {
        const response = await axiosInstance.post("/regionGroups", { name, timezone, vessel_ids }, { meta: { showSnackbar: true } });
        return response.data;
    }

    async update({ id, name, timezone, vessel_ids }) {
        const response = await axiosInstance.post(`/regionGroups/${id}`, { name, timezone, vessel_ids }, { meta: { showSnackbar: true } });
        return response.data;
    }

    async delete({ id }) {
        const response = await axiosInstance.delete(`/regionGroups/${id}`, { meta: { showSnackbar: true } });
        return response.data;
    }
}

const regionGroupController = new RegionGroupController();

export default regionGroupController;
