const express = require("express");
const hasPermission = require("../middlewares/hasPermission");
const { permissions } = require("../utils/permissions");
const { validateData } = require("../middlewares/validator");
const { body, param } = require("express-validator");
const { validateError } = require("../utils/functions");
const { default: mongoose } = require("mongoose");
const { isValidObjectId } = require("mongoose");
const { isValidTimezoneOffset } = require("../utils/timezonesList");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const regionGroupService = require("../services/RegionGroup.service");
const RegionGroup = require("../models/RegionGroup");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_REGION_GROUPS), isAuthenticated, async (req, res) => {
    try {
        const regionGroups = await regionGroupService.find();
        res.json(regionGroups);
    } catch (err) {
        validateError(err, res);
    }
});

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_REGION_GROUP),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRegionsGroups]),
    validateData.bind(this, [
        body("name")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("timezone")
            .isString()
            .notEmpty()
            .custom(isValidTimezoneOffset)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("vessel_ids")
            .isArray()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("vessel_ids.*")
            .isString()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { name, timezone, vessel_ids } = req.body;
            const objectIds = vessel_ids.map((id) => mongoose.Types.ObjectId(id));
            const vesselsAlreadyInGroup = await RegionGroup.find({ vessel_ids: { $in: objectIds } }, { _id: 1 });
            if (vesselsAlreadyInGroup.length > 0) {
                res.status(409).json({ message: `Vessel is already assigned to a region group.` });
                return;
            }
            const regionGroup = await regionGroupService.create({
                name,
                timezone,
                vessel_ids: vessel_ids.map((id) => mongoose.Types.ObjectId(id)),
                created_by: req.user._id,
            });
            res.json({ message: `Region group has been created`, regionGroup });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/:id",
    assignEndpointId.bind(this, endpointIds.UPDATE_REGION_GROUP),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRegionsGroups]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("name")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("timezone")
            .isString()
            .notEmpty()
            .custom(isValidTimezoneOffset)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("vessel_ids")
            .isArray()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("vessel_ids.*")
            .isString()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const data = req.body;
            const { vessel_ids } = data;
            const objectIds = vessel_ids.map((id) => mongoose.Types.ObjectId(id));
            const vesselsAlreadyInGroup = await RegionGroup.find({ _id: { $ne: req.params.id }, vessel_ids: { $in: objectIds } }, { _id: 1 });
            if (vesselsAlreadyInGroup.length > 0) {
                res.status(409).json({ message: `Vessel is already assigned to a region group.` });
                return;
            }
            const result = await regionGroupService.update({ id: req.params.id, ...data });

            if (!result) return res.status(400).json({ message: `Region group does not exist` });

            return res.json({ message: `Region group '${data.name}' has been edited`, regionGroup: result });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.delete(
    "/:id",
    assignEndpointId.bind(this, endpointIds.DELETE_REGION_GROUP),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRegionsGroups]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { id } = req.params;

            const result = await regionGroupService.delete({ id });
            if (!result) return res.status(400).json({ message: `Region group does not exist` });

            return res.json({ message: `Region group has been deleted` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Region Groups
 *   description: Fetch region groups
 * components:
 *   schemas:
 *     RegionGroup:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document Id of the region group
 *           example: 67942a74a7f838634a00190a
 *         name:
 *           type: string
 *           description: Name of the region group
 *           example: Philipines
 *         timezone:
 *           type: string
 *           description: Timezone of the region group
 *           example: +8:00
 *         vessel_ids:
 *           type: array
 *           items:
 *             type: string
 *           description: List of vessel ids in the region group
 *           example: ["67942a74a7f838634a00190a", "67942a74a7f838634a00190b"]
 *         vessels:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               vessel_id:
 *                 type: string
 *                 description: Document Id of the vessel
 *                 example: 67942a74a7f838634a00190a
 *               unit_id:
 *                 type: string
 *                 description: Unit id of the camera
 *                 example: prototype-24
 *               name:
 *                 type: string
 *                 description: Name of the vessel
 *                 example: BRP Bagacay (MRRV-4410)
 *         created_by:
 *           type: object
 *           properties:
 *             _id:
 *               type: string
 *               description: Document Id of the user
 *               example: 67942a74a7f838634a00190a
 *             name:
 *               type: string
 *               description: Name of the user
 *               example: John Doe
 *         creationTimestamp:
 *           type: string
 *           description: Creation timestamp of the region group
 *           example: 2025-06-24T10:00:00.000Z
 */

/**
 * @swagger
 * /regionGroups:
 *   get:
 *     summary: Fetch all region groups
 *     description: Rate limited to 15 requests every 5 seconds
 *     tags: [Region Groups]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: A list of region groups
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/RegionGroup'
 *       500:
 *         description: Server error
 */
