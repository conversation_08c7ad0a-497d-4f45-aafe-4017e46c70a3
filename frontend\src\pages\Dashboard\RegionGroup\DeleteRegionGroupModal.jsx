import { Button, Grid, Modal, Typography } from "@mui/material";
import ModalContainer from "../../../components/ModalContainer";
import regionGroupController from "../../../controllers/RegionGroup.controller";
import useVesselInfo from "../../../hooks/VesselInfoHook";
import useGroupRegions from "../../../hooks/GroupRegionHook";

const DeleteRegionGroupModal = ({ regionGroup, setRegionGroup, setDeleting, onSuccess }) => {
    const handleClose = () => {
        setRegionGroup();
    };
    const { fetchVesselsInfo } = useVesselInfo();
    const { fetchRegions } = useGroupRegions();

    const onDelete = () => {
        handleClose();
        setDeleting(regionGroup._id);
        regionGroupController
            .delete({ id: regionGroup._id })
            .then(() => onSuccess && onSuccess(regionGroup))
            .catch(console.error)
            .finally(() => setDeleting());
        fetchVesselsInfo();
        fetchRegions();
    };

    return (
        <Modal open={Boolean(regionGroup)} onClose={handleClose}>
            <ModalContainer title={"Delete Region Group"} onClose={handleClose}>
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: "auto" }}>
                    <Grid>
                        <Typography>Are you sure you want to delete region group &quot;{regionGroup?.name}&quot;?</Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" onClick={handleClose} className="btn-cancel">
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" color="error" onClick={onDelete} sx={{ textTransform: "none", padding: "10px 24px" }}>
                                Delete
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default DeleteRegionGroupModal;
