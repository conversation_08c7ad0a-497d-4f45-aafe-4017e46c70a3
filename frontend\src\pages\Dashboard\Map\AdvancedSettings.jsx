import { Grid, Typography, Slider, Tooltip, IconButton, alpha } from "@mui/material";
import { Info, Replay } from "@mui/icons-material";
import theme from "../../../theme";
// import CustomDateTimePicker from "../../../components/CustomDateTimePicker";
// import { useApp } from "../../../hooks/AppHook";

const AdvancedSettings = ({
    // eslint-disable-next-line no-unused-vars
    interval,
    precision,
    datapointsDistance,
    precisionSliderKey,
    disableAdvancedSettingsReset,
    resetAdvancedSettings,
    setInterval,
    setPrecision,
    setDatapointsDistance,
    temporaryInterval,
    setTemporaryInterval,
    // showFilteredCoordinates,
    // setShowFilteredCoordinates,
    // fromDate,
    // setFromDate,
    // toDate,
    // setToDate,
    // showCoordinatesPoints,
    // setShowCoordinatesPoints,
}) => {
    // const { devMode } = useApp();
    // const [isCheckboxDisabled, setIsCheckboxDisabled] = useState(false);

    // const handleFilteredCoordinatesChange = (e) => {
    //     if (!isCheckboxDisabled) {
    //         setShowFilteredCoordinates(e.target.checked);
    //         setIsCheckboxDisabled(true);
    //         setTimeout(() => {
    //             setIsCheckboxDisabled(false);
    //         }, 5000);
    //     }
    // };

    return (
        <Grid
            container
            flexDirection={"column"}
            color={"#FFFFFF"}
            gap={2}
            paddingX={0}
            paddingY={2}
            position={"relative"}
            className="map-step-6"
            sx={{ maxWidth: { xs: "none", md: 380 }, width: "100%" }}
        >
            <IconButton
                disabled={disableAdvancedSettingsReset}
                disableRipple
                sx={{ width: 15, height: 15, marginLeft: 1, position: "absolute", top: { xs: "-50px", md: "-30px" }, right: { xs: "0", md: "35px" } }}
                onClick={(e) => {
                    e.stopPropagation();
                    resetAdvancedSettings();
                }}
            >
                <Replay fontSize="small" sx={{ opacity: disableAdvancedSettingsReset && 0.5 }} />
            </IconButton>
            <Grid container flexDirection={"column"} paddingRight={2}>
                <Grid justifyContent={"space-between"} display={"flex"} alignItems={"center"} gap={1}>
                    <Typography textAlign={"center"} justifyContent={"center"} color={theme.palette.custom.mediumGrey} fontSize={"12px"}>
                        Interval
                    </Typography>
                    <Tooltip
                        enterDelay={300}
                        title="Change the minimum interval between each captured coordinate. e.g., if interval is 5m, then all the points on the polyline will be at least 5 minutes apart."
                    >
                        <Info
                            sx={{
                                color: theme.palette.custom.mediumGrey,
                                backgroundColor: alpha(theme.palette.custom.offline, 0.08),
                                fontSize: "18px",
                            }}
                        />
                    </Tooltip>
                </Grid>
                <Grid paddingLeft={2}>
                    <Slider
                        valueLabelFormat={(v) => `${v}m`}
                        valueLabelDisplay="auto"
                        value={temporaryInterval}
                        onChange={(e, v) => setTemporaryInterval(v)}
                        onChangeCommitted={(e, v) => setInterval(v)}
                        min={1}
                        max={60}
                        sx={{
                            padding: 0,
                            color: theme.palette.custom.mediumGrey,
                        }}
                    />
                </Grid>
            </Grid>
            <Grid container flexDirection={"column"} paddingRight={2}>
                <Grid justifyContent={"space-between"} display={"flex"} alignItems={"center"} gap={1}>
                    <Typography textAlign={"center"} justifyContent={"center"} color={theme.palette.custom.mediumGrey} fontSize={"12px"}>
                        Precision
                    </Typography>
                    <Tooltip
                        enterDelay={300}
                        title="Change the precision of the coordinates degrees in decimal places. e.g., if precision is 3d, then coordinates (10.754019, 115.7928663) will be rounded up to (10.754, 115.793). This will reduce the amount of points on the polyline especially if a vessel is standing still."
                    >
                        <Info
                            sx={{
                                color: theme.palette.custom.mediumGrey,
                                backgroundColor: alpha(theme.palette.custom.offline, 0.08),
                                fontSize: "18px",
                            }}
                        />
                    </Tooltip>
                </Grid>
                <Grid paddingLeft={2}>
                    <Slider
                        key={precisionSliderKey}
                        valueLabelFormat={(v) => `${v}d`}
                        valueLabelDisplay="auto"
                        sx={{
                            padding: 0,
                            color: theme.palette.custom.mediumGrey,
                        }}
                        defaultValue={precision}
                        onChangeCommitted={(e, v) => setPrecision(v)}
                        min={3}
                        max={15}
                    />
                </Grid>
            </Grid>
            <Grid container flexDirection={"column"} paddingRight={2}>
                <Grid justifyContent={"space-between"} display={"flex"} alignItems={"center"} gap={1}>
                    <Typography textAlign={"center"} justifyContent={"center"} color={theme.palette.custom.mediumGrey} fontSize={"12px"}>
                        Distance b/w Data Points
                    </Typography>
                    <Tooltip
                        enterDelay={300}
                        title="Change the minimum distance between data points. Decreasing this value may cause performance drop."
                    >
                        <Info
                            sx={{
                                color: theme.palette.custom.mediumGrey,
                                fontSize: "18px",
                            }}
                        />
                    </Tooltip>
                </Grid>
                <Grid paddingLeft={2}>
                    <Slider
                        valueLabelFormat={(v) => `${v} meters`}
                        valueLabelDisplay="auto"
                        sx={{
                            padding: 0,
                            color: theme.palette.custom.mediumGrey,
                        }}
                        value={datapointsDistance}
                        onChange={(e, v) => setDatapointsDistance(v)}
                        min={50}
                        max={10000}
                    />
                </Grid>
            </Grid>
            {/* <Grid item container flexDirection={"column"} paddingRight={2}>
                <Grid item justifyContent={"space-between"} display={"flex"} alignItems={"center"} gap={1}>
                    <Typography textAlign={"center"} justifyContent={"center"} color={theme.palette.custom.mediumGrey} fontSize={"12px"}>
                        Coordinate Filters
                    </Typography>
                </Grid>
                <Grid item container>
                    <Grid item display={"flex"} alignItems={"center"} justifyContent={"space-between"} width={"100%"}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={showFilteredCoordinates}
                                    onChange={handleFilteredCoordinatesChange}
                                    disabled={isCheckboxDisabled}
                                    sx={{
                                        color: theme.palette.custom.mediumGrey,
                                        "&.Mui-checked": {
                                            color: theme.palette.custom.mediumGrey,
                                        },
                                    }}
                                />
                            }
                            label="Show true journey path"
                            sx={{
                                color: theme.palette.custom.mediumGrey,
                                fontSize: "12px",
                            }}
                        />
                        <Tooltip
                            enterDelay={300}
                            title="When disabled, filters out coordinates based on time gaps and stationary status. When enabled, shows all coordinates."
                        >
                            <Info
                                sx={{
                                    color: theme.palette.custom.mediumGrey,
                                    fontSize: "18px",
                                }}
                            />
                        </Tooltip>
                    </Grid>
                    {devMode && (
                        <>
                            <Grid item display={"flex"} alignItems={"center"} justifyContent={"space-between"} width={"100%"}>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={showCoordinatesPoints}
                                            onChange={(e) => setShowCoordinatesPoints(e.target.checked)}
                                            sx={{
                                                color: theme.palette.custom.mediumGrey,
                                                "&.Mui-checked": {
                                                    color: theme.palette.custom.mediumGrey,
                                                },
                                            }}
                                        />
                                    }
                                    label="Show Coordinate Points"
                                    sx={{
                                        color: theme.palette.custom.mediumGrey,
                                        fontSize: "12px",
                                    }}
                                />
                                <Tooltip enterDelay={300} title="Shows two types of coordinates points on the map: Stationary(Red) and Moving(Blue).">
                                    <Info
                                        sx={{
                                            color: theme.palette.custom.mediumGrey,
                                            fontSize: "18px",
                                        }}
                                    />
                                </Tooltip>
                            </Grid>
                            <Grid item display={"flex"} alignItems={"center"} justifyContent={"space-between"} width={"100%"}>
                                <Grid item container flexDirection={"column"}>
                                    <Grid item paddingLeft={2}></Grid>
                                    {showCoordinatesPoints && (
                                        <Grid item container color={"#FFFFFF"} gap={1}>
                                            <Grid item container xs flexDirection={"column"} justifyContent={"center"}>
                                                <Grid item display={"flex"}>
                                                    <Typography
                                                        textAlign={"center"}
                                                        justifyContent={"center"}
                                                        color={(theme) => theme.palette.custom.mediumGrey}
                                                        fontSize={"16px"}
                                                    >
                                                        From
                                                    </Typography>
                                                </Grid>
                                                <Grid item>
                                                    <CustomDateTimePicker
                                                        label="From Date"
                                                        value={fromDate}
                                                        onChange={(newValue) => setFromDate(newValue)}
                                                    />
                                                </Grid>
                                            </Grid>
                                            <Grid item container xs flexDirection={"column"}>
                                                <Grid item display={"flex"}>
                                                    <Typography
                                                        textAlign={"center"}
                                                        justifyContent={"center"}
                                                        color={(theme) => theme.palette.custom.mediumGrey}
                                                        fontSize={"16px"}
                                                    >
                                                        To
                                                    </Typography>
                                                </Grid>
                                                <Grid item>
                                                    <CustomDateTimePicker
                                                        label="To Date"
                                                        value={toDate}
                                                        onChange={(newValue) => setToDate(newValue)}
                                                        minDateTime={fromDate}
                                                    />
                                                </Grid>
                                            </Grid>
                                        </Grid>
                                    )}
                                </Grid>
                            </Grid>
                        </>
                    )}
                </Grid>
            </Grid> */}
        </Grid>
    );
};

export default AdvancedSettings;
